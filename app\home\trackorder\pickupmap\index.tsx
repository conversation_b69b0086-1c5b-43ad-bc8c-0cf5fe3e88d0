import * as Location from "expo-location";
import BackArrow from "../../../../assets/icons/BackArrow.svg";
import DirectionsIcon from "../../../../assets/Hometab/ic_outline-directions.svg";
import DotIcon from "../../../../assets/Hometab/doticon.svg";
import HeadSetIcon from "../../../../assets/Hometab/lucide_headset.svg";
import LocationIcon from "../../../../assets/Hometab/drop_location.svg";
import MapView, { Marker, Polyline } from "react-native-maps";
import MessageIcon from "../../../../assets/Hometab/ic_round-message.svg";
import PhoneIcon from "../../../../assets/Hometab/mingcute_phone-fill.svg";
import PopUpComponent from "../../../../component/PopUpModle/PopUpComponent";
import React, { useEffect, useRef, useState } from "react";
import VegIcon from "../../../../assets/Hometab/mdi_lacto-vegetarian.svg";
import { router, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";

import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  Linking,
  Platform,
} from "react-native";

const index = () => {
  const [region, setRegion] = useState<any>(null);
  const [userLocation, setUserLocation] = useState<any>(null);
  const [routeCoordinates, setRouteCoordinates] = useState<any[]>([]);
  const [showDirections, setShowDirections] = useState(false);
  const [routeDistance, setRouteDistance] = useState<string>("");
  const [routeDuration, setRouteDuration] = useState<string>("");
  const mapRef = useRef<MapView>(null);
  const {
    id,
    shop_name,
    logo,
    banner_image,
    address,
    latitude,
    longitude,
    distance,
    IsOrderPickedUp,
    orderNumber,
    otp,
    invoice_no,
  } = useLocalSearchParams();
  const [showissueMenu, setshowissueMenu] = useState(false);

  // Convert string coordinates to numbers
  const destinationLat = parseFloat(latitude as string);
  const destinationLng = parseFloat(longitude as string);
  const requestLocationPermission = async () => {
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission denied",
        "Allow location access to use this feature."
      );
      return;
    }

    let location = await Location.getCurrentPositionAsync({});
    const { latitude, longitude } = location.coords;

    // Store user location
    setUserLocation({ latitude, longitude });

    // Create region that includes both user location and destination
    const midLat = (latitude + destinationLat) / 2;
    const midLng = (longitude + destinationLng) / 2;
    const latDelta = Math.abs(latitude - destinationLat) * 1.5;
    const lngDelta = Math.abs(longitude - destinationLng) * 1.5;

    const newRegion = {
      latitude: midLat,
      longitude: midLng,
      latitudeDelta: Math.max(latDelta, 0.01),
      longitudeDelta: Math.max(lngDelta, 0.01),
    };
    setRegion(newRegion);
    if (mapRef.current) {
      mapRef.current.animateToRegion(newRegion, 1000);
    }
  };

  // Function to calculate distance between two coordinates
  const calculateDistance = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ) => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLon = ((lon2 - lon1) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in kilometers
    return distance;
  };

  // Function to get directions (simplified version without API key requirement)
  const getDirections = async () => {
    if (!userLocation) {
      Alert.alert(
        "Location not available",
        "Please wait for location to be detected."
      );
      return;
    }

    // Calculate straight-line distance and estimated time
    const distance = calculateDistance(
      userLocation.latitude,
      userLocation.longitude,
      destinationLat,
      destinationLng
    );

    const estimatedTime = Math.round((distance / 40) * 60); // Assuming 40 km/h average speed

    // Create a simple straight line route
    setRouteCoordinates([
      { latitude: userLocation.latitude, longitude: userLocation.longitude },
      { latitude: destinationLat, longitude: destinationLng },
    ]);

    setRouteDistance(`${distance.toFixed(1)} km`);
    setRouteDuration(`${estimatedTime} min`);
    setShowDirections(true);

    // Optional: Try to get more accurate directions from Google API if available
    // Uncomment and add your API key to use Google Directions API
    /*
    try {
      const apiKey = "YOUR_GOOGLE_MAPS_API_KEY";
      if (apiKey !== "YOUR_GOOGLE_MAPS_API_KEY") {
        const origin = `${userLocation.latitude},${userLocation.longitude}`;
        const destination = `${destinationLat},${destinationLng}`;

        const response = await fetch(
          `https://maps.googleapis.com/maps/api/directions/json?origin=${origin}&destination=${destination}&key=${apiKey}`
        );

        const data = await response.json();

        if (data.routes && data.routes.length > 0) {
          const route = data.routes[0];
          const points = route.overview_polyline.points;
          const decodedPoints = decodePolyline(points);

          setRouteCoordinates(decodedPoints);
          setRouteDistance(route.legs[0].distance.text);
          setRouteDuration(route.legs[0].duration.text);
        }
      }
    } catch (error) {
      console.log("Using fallback route calculation");
    }
    */
  };

  // Function to decode polyline (simplified version)
  const decodePolyline = (encoded: string) => {
    const points = [];
    let index = 0;
    let lat = 0;
    let lng = 0;

    while (index < encoded.length) {
      let b;
      let shift = 0;
      let result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlat = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlng = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
      lng += dlng;

      points.push({
        latitude: lat / 1e5,
        longitude: lng / 1e5,
      });
    }
    return points;
  };

  // Function to open external navigation apps
  const openExternalNavigation = () => {
    const destination = `${destinationLat},${destinationLng}`;
    const label = encodeURIComponent(shop_name as string);

    Alert.alert("Open Navigation", "Choose your preferred navigation app:", [
      {
        text: "Google Maps",
        onPress: () => {
          const url = Platform.select({
            ios: `comgooglemaps://?daddr=${destination}&directionsmode=driving`,
            android: `google.navigation:q=${destination}`,
          });

          Linking.canOpenURL(url as string).then((supported) => {
            if (supported) {
              Linking.openURL(url as string);
            } else {
              // Fallback to web version
              const webUrl = `https://www.google.com/maps/dir/?api=1&destination=${destination}&destination_place_id=${label}`;
              Linking.openURL(webUrl);
            }
          });
        },
      },
      {
        text: "Apple Maps",
        onPress: () => {
          const url = `http://maps.apple.com/?daddr=${destination}&dirflg=d`;
          Linking.openURL(url);
        },
      },
      {
        text: "Show Route on Map",
        onPress: getDirections,
      },
      {
        text: "Cancel",
        style: "cancel",
      },
    ]);
  };

  useEffect(() => {
    requestLocationPermission();
  }, []);

  return (
    <SafeAreaView className="flex-1 justify-between">
      <ScrollView>
        <View className="px-4 mt-3">
          <View className="flex-row relative items-center justify-start mt-4">
            <TouchableOpacity
              onPress={() => {
                router.back();
              }}
              className="absolute"
            >
              <View>
                <BackArrow />
              </View>
            </TouchableOpacity>
            <View className="flex-1 items-center justify-center">
              <Text className="font-[400] text-[19px] leading-[39px]">
                Go to pickup
              </Text>
              <Text className="">Order ID: {orderNumber}</Text>
              {userLocation && (
                <TouchableOpacity
                  onPress={getDirections}
                  className="mt-2 bg-[#00660A] px-3 py-1 rounded-full"
                >
                  <Text className="text-white text-[12px] font-semibold">
                    Show Route
                  </Text>
                </TouchableOpacity>
              )}
            </View>
            <View className="relative">
              <TouchableOpacity
                onPress={() => {
                  setshowissueMenu(!showissueMenu);
                }}
              >
                <DotIcon />
              </TouchableOpacity>
              {showissueMenu && (
                <TouchableOpacity
                  onPress={() => {
                    router.push("/home/<USER>/pickupmap/issue");
                  }}
                  className="right-3 z-10 top-6 absolute flex-row space-x-2 h-[45px] w-[138px] items-center justify-center bg-[#000] rounded-[8px]"
                >
                  <HeadSetIcon />
                  <Text className="font-[400] font-Pop text-[#fff] leading-[21px] text-[14px]">
                    Report issue
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
        <View className="relative h-[400px] mt-7">
          <MapView
            style={StyleSheet.absoluteFill}
            region={region}
            provider={"google"}
            ref={mapRef}
            showsUserLocation
            zoomEnabled={true}
            onRegionChangeComplete={setRegion}
            key={1}
          >
            {/* Destination Marker */}
            <Marker
              coordinate={{
                latitude: destinationLat,
                longitude: destinationLng,
              }}
              title={shop_name as string}
              description={address as string}
              pinColor="red"
            />

            {/* Route Polyline */}
            {showDirections && routeCoordinates.length > 0 && (
              <Polyline
                coordinates={routeCoordinates}
                strokeColor="#00660A"
                strokeWidth={4}
                lineDashPattern={[5, 5]}
              />
            )}
          </MapView>

          {/* Route Information Overlay */}
          {showDirections && (routeDistance || routeDuration) && (
            <View className="absolute top-4 left-4 right-4 bg-white rounded-lg p-3 shadow-lg">
              <View className="flex-row justify-between items-center">
                <View>
                  {routeDistance && (
                    <Text className="font-semibold text-[16px] text-[#00660A]">
                      Distance: {routeDistance}
                    </Text>
                  )}
                  {routeDuration && (
                    <Text className="text-[14px] text-gray-600">
                      Duration: {routeDuration}
                    </Text>
                  )}
                </View>
                <TouchableOpacity
                  onPress={() => setShowDirections(false)}
                  className="bg-gray-200 rounded-full p-2"
                >
                  <Text className="text-[12px] font-semibold">✕</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
        <View className="mt-6 px-4 flex-row items-start justify-between">
          <View
            className="flex-row space-x-6 flex-1"
            style={{
              alignItems: "flex-start",
            }}
          >
            <View className="">
              <LocationIcon />
            </View>
            <View className="space-y-4">
              <Text className="">{shop_name}</Text>
              <Text className="pr-6">{address}</Text>
              {/* <View className="flex-row items-center space-x-6 mt-4">
                <PhoneIcon />
                <MessageIcon />
              </View> */}
            </View>
          </View>
          <TouchableOpacity
            className="items-center justify-center space-y-2"
            onPress={openExternalNavigation}
          >
            <DirectionsIcon />
            <Text className="text-[#00660A] font-semibold">Get Directions</Text>
          </TouchableOpacity>
        </View>
        <View className="mt-5 p-4">
          <View className="items-center p-4 justify-center border-[#E9EAE9] border-[1px] rounded-[4px]">
            <Text className="">Share this OTP with your seller partner </Text>

            <View className="flex-row items-center space-x-4">
              <View className="mt-4 h-[24px] w-[24px] items-center justify-center border-[1px] border-[#00660A]">
                <Text className="">{String(otp).split("")[0]}</Text>
              </View>
              <View className="mt-4 h-[24px] w-[24px] items-center justify-center border-[1px] border-[#00660A]">
                <Text className="">{String(otp).split("")[1]}</Text>
              </View>
              <View className="mt-4 h-[24px] w-[24px] items-center justify-center border-[1px] border-[#00660A]">
                <Text className="">{String(otp).split("")[2]}</Text>
              </View>
              <View className="mt-4 h-[24px] w-[24px] items-center justify-center border-[1px] border-[#00660A]">
                <Text className="">{String(otp).split("")[3]}</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
      <View className="my-4 px-4">
        <TouchableOpacity
          onPress={() => {
            router.push({
              pathname: "/home/<USER>/pickupmap/confirmpickup",
              params: {
                id: id,
                shop_name: shop_name,
                logo: logo,
                banner_image: banner_image,
                address: address,
                latitude: latitude,
                longitude: longitude,
                orderNumber: orderNumber,
                distance: distance,
                IsOrderPickedUp: IsOrderPickedUp,
                otp: otp,
                invoice_no: invoice_no,
              },
            });
          }}
          className="h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
        >
          <Text className="font-[400] font-Pop text-[16px] leading-[24px] text-[#fff]">
            Reached Pickup Location
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default index;
